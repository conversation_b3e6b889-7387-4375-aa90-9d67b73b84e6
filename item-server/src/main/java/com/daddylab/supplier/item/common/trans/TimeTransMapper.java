package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Optional;

@Mapper
public interface TimeTransMapper {
    TimeTransMapper INSTANCE = Mappers.getMapper(TimeTransMapper.class);

    /**
     * 时间戳转LocalDateTime
     *
     * @param timestamp 时间戳
     * @return LocalDateTime
     */
    default LocalDateTime timestampToLocalDateTime(Long timestamp) {
        return DateUtil.toLocalDateTime(timestamp);
    }

    /**
     * 将时间戳格式化为字符串
     *
     * @param timestamp 时间戳
     * @return 格式化时间
     */
    default Long formattedStringToTimestamp(String timestamp) {
        final LocalDateTime time = DateUtil.parseCompatibility(timestamp);
        if (time == null) {
            return null;
        }
        return DateUtil.toEpochSecond(time);
    }

    /**
     * 时间戳对象转长整形时间戳（秒级）
     *
     * @param timestamp 时间戳
     * @return Long
     */
    default Long timestampToLong(Timestamp timestamp) {
        return Optional.ofNullable(timestamp)
                .map(Timestamp::getTime)
                .map(time -> time / 1000)
                .orElse(0L);
    }

    /**
     * Date 转 长整形时间戳（秒级）
     *
     * @param date Date
     * @return Long 长整形时间戳（秒级）
     */
    default Long dateToLong(Date date) {
        return Optional.ofNullable(date).map(Date::getTime).map(time -> time / 1000).orElse(0L);
    }

    /**
     * 将时间戳格式化为字符串
     *
     * @param timestamp 时间戳
     * @return 格式化时间
     */
    default String timestampToFormattedString(Long timestamp) {
        return DateUtil.format(timestamp);
    }

    /**
     * 将LocalDateTime格式化
     *
     * @param localDateTime localDateTime
     * @return 格式化时间
     */
    default String localDateTimeToFormattedString(LocalDateTime localDateTime) {
        return DateUtil.format(localDateTime);
    }

    /**
     * LocalDateTime转化为时间戳
     *
     * @param localDateTime localDateTime
     * @return 时间戳（秒）
     */
    default Long localDateTimeToTimestamp(LocalDateTime localDateTime) {
        return localDateTime == null ? 0L : localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }
}
