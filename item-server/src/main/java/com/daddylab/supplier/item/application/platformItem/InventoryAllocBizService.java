package com.daddylab.supplier.item.application.platformItem;

import com.daddylab.supplier.item.application.platformItem.data.WarehouseInventoryDetailItemVO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAlloc;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku;
import com.daddylab.supplier.item.types.stockSpec.ShopSpecAllocableStockVO;

import java.util.Collection;
import java.util.List;

public interface InventoryAllocBizService {
    
    void allocInventory();
    
    void allocInventory(Collection<String> skuCodes);
    
    int cleanInvalidAllocRecords(List<InventoryAllocShop> allocShops);
}
