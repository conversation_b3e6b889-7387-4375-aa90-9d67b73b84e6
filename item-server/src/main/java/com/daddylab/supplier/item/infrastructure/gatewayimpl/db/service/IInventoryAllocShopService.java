package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.InventoryAllocShop;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.IDaddyService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 库存分配店铺设置 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
public interface IInventoryAllocShopService extends IDaddyService<InventoryAllocShop> {
    
    InventoryAllocShop getByShopNo(String shopNo);
    
    List<InventoryAllocShop> listByShopNo(Collection<String> shopNos);
    
    void syncSyncEnabledGlobalConfig(List<PlatformItem> platformItems);
}
