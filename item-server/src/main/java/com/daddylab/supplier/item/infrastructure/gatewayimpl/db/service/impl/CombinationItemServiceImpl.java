package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.CombinationItemPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.sqlInjector.DaddyServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationNameDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ComposeSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IComposeSkuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-11
 */
@Service
public class CombinationItemServiceImpl
        extends DaddyServiceImpl<CombinationItemMapper, CombinationItem>
        implements ICombinationItemService {
    
    @Autowired
    CombinationItemMapper combinationItemMapper;
    @Autowired
    IComposeSkuService composeSkuService;
    
    @Override
    public List<ComposeSkuDO> listSkuDetail(ComposeSkuPageQuery query) {
        query.setOffsetVal(query.getOffset());
        return combinationItemMapper.listSkuDetail(
                query, query.getSkuIdList(), query.getSkuCodeList());
    }
    
    @Override
    public Integer countSkuDetail(ComposeSkuPageQuery query) {
        return combinationItemMapper.countSkuDetail(
                query, query.getSkuIdList(), query.getSkuCodeList());
    }
    
    @Override
    public List<CombinationDO> listItem(CombinationItemPageQuery query) {
        query.setOffsetVal((long) query.getOffset());
        return combinationItemMapper.listItem(query);
    }
    
    @Override
    public Integer countListItem(CombinationItemPageQuery query) {
        return combinationItemMapper.countListItem(query);
    }
    
    @Override
    public CombinationItem getByItemCode(String itemCode) {
        LambdaQueryWrapper<CombinationItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CombinationItem::getCode, itemCode);
        List<CombinationItem> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        } else {
            return list.get(0);
        }
    }
    
    @Override
    public List<CombinationProviderDO> queryProviderList(List<String> combinationCodeList) {
        return combinationItemMapper.queryProviderList(combinationCodeList);
    }
    
    @Override
    public List<CombinationNameDO> queryNameList(List<String> codeList) {
        return combinationItemMapper.queryNameList(codeList);
    }
    
    @Override
    public Boolean verifyCode(String itemCode) {
        LambdaQueryWrapper<CombinationItem> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CombinationItem::getCode, itemCode);
        return this.count(wrapper) > 0;
    }
    
    @Override
    public Long isCombinationItem(String code) {
        return lambdaQuery()
                .eq(CombinationItem::getCode, code)
                .select(CombinationItem::getId)
                .oneOpt()
                .map(CombinationItem::getId)
                .orElse(0L);
    }
    
    @Override
    public Map<String, Long> isCombinationItem(List<String> codes) {
        if (CollUtil.isEmpty(codes)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(CombinationItem::getCode, codes)
                .select(CombinationItem::getId, CombinationItem::getCode)
                .list()
                .stream()
                .collect(Collectors.toMap(CombinationItem::getCode, CombinationItem::getId));
    }
    
    @Override
    public List<CombinationItem> listByCombinationCode(Collection<String> combinationCodes) {
        if (CollUtil.isEmpty(combinationCodes)) {
            return Collections.emptyList();
        }
        return lambdaQuery().in(CombinationItem::getCode, combinationCodes).list();
    }
    
    @Override
    public List<CombinationItem> listBySkuCode(String skuCode) {
        List<ComposeSku> composeSkus =
                composeSkuService.lambdaQuery().eq(ComposeSku::getSkuCode, skuCode).list();
        if (composeSkus.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> combinationIds =
                composeSkus.stream().map(ComposeSku::getCombinationId).collect(Collectors.toSet());
        return listByIds(combinationIds);
    }
    
    @Override
    public List<CombinationItem> listBySkuCode(List<String> skuCodes) {
        List<ComposeSku> composeSkus =
                composeSkuService.lambdaQuery().in(ComposeSku::getSkuCode, skuCodes).list();
        if (composeSkus.isEmpty()) {
            return Collections.emptyList();
        }
        Set<Long> combinationIds =
                composeSkus.stream().map(ComposeSku::getCombinationId).collect(Collectors.toSet());
        return listByIds(combinationIds);
        
    }
    
    @Override
    public Map<String, List<CombinationItem>> mapListBySkuCode(List<String> skuCodes) {
        List<ComposeSku> composeSkus =
                composeSkuService.lambdaQuery().in(ComposeSku::getSkuCode, skuCodes).list();
        if (composeSkus.isEmpty()) {
            return Collections.emptyMap();
        }

        Set<Long> combinationIds =
                composeSkus.stream().map(ComposeSku::getCombinationId).collect(Collectors.toSet());
        List<CombinationItem> combinationItems = listByIds(combinationIds);

        return buildSkuCodeToCombinationItemsMap(skuCodes, composeSkus, combinationItems);
    }

    /**
     * 构建SKU编码到组合商品列表的映射关系
     * 解决潜在的重复问题，提升代码可读性
     */
    private Map<String, List<CombinationItem>> buildSkuCodeToCombinationItemsMap(
            List<String> skuCodes,
            List<ComposeSku> composeSkus,
            List<CombinationItem> combinationItems) {

        // 构建组合商品ID到组合商品的映射，提升查找效率
        Map<Long, CombinationItem> combinationItemMap = combinationItems.stream()
                .collect(Collectors.toMap(CombinationItem::getId, Function.identity()));

        // 为每个SKU编码构建对应的组合商品列表
        Map<String, List<CombinationItem>> resultMap = new HashMap<>();
        for (String skuCode : skuCodes) {
            List<CombinationItem> matchedCombinationItems = findCombinationItemsBySkuCode(
                    skuCode, composeSkus, combinationItemMap);
            resultMap.put(skuCode, matchedCombinationItems);
        }

        return resultMap;
    }

    /**
     * 根据SKU编码查找对应的组合商品列表
     * 使用Set去重，避免重复的组合商品
     */
    private List<CombinationItem> findCombinationItemsBySkuCode(
            String skuCode,
            List<ComposeSku> composeSkus,
            Map<Long, CombinationItem> combinationItemMap) {

        Set<CombinationItem> uniqueCombinationItems = new LinkedHashSet<>();

        for (ComposeSku composeSku : composeSkus) {
            if (skuCode.equals(composeSku.getSkuCode())) {
                CombinationItem combinationItem = combinationItemMap.get(composeSku.getCombinationId());
                if (combinationItem != null) {
                    uniqueCombinationItems.add(combinationItem);
                }
            }
        }

        return new ArrayList<>(uniqueCombinationItems);
    }
}
