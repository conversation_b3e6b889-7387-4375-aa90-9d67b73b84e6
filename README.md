## 项目基本结构说明
### 搭建环境
idea 2021.1 CA + maven 3.8 + JDK8
## 项目结构


| 模块 | 定义 | 结构|
| --- | ---  | --- |
| `xxx-doc` | some scripts, such as sql scripts and json data | package ||
| `xxx-api` | this is just the interface definition exposed to the outside world | module ||
| `xxx-api-dto` | data transfer object，parameters required by the interface | package ||
| `xxx-api-service` | only interface definition | package ||
| `xxx-server` | service core | module ||
| `xxx-server-adapter` |  | package ||
| `xxx-server-application` |  | package ||
| `xxx-server-domain` |  | package ||
| `xxx-server-infrastructure` |  | package ||
| `xxx-server-infrastructure-gateway` |  | package ||


后端项目，目前的项目结构如下：



## 技术栈

### 目前已引入

| 框架 | 说明 |  版本 |
| --- | --- | --- |
| [Spring Boot Dependencies](https://spring.io/projects/spring-boot) | 应用开发框架(！！！核心依赖) |   2.3.7.RELEASE |
| [spring-cloud-alibaba](https://github.com/alibaba/spring-cloud-alibaba) | 应用开发框架(！！！核心依赖) |   2.2.3.RELEASE |
| [COLA](https://github.com/alibaba/COLA) | 整洁面向对象分层架构(！！！核心依赖) | 4.0.1 |
| [MyBatis-Plus](https://mp.baomidou.com/) | Mybatis 增强工具包 | 3.4.0 |
| [Redisson](https://github.com/redisson/redisson) | Redisson 客户端 | 3.13.6 |
| [Dubbo](http://dubbo.apache.org/) | 分布式 RPC 服务框架 | 2.7.8 |
| [Hutool](https://hutool.cn/) | 工具集 | 5.7.11 |
| [Nacos](https://nacos.io/zh-cn/) | 配置中心+服务注册中心 | 2.0.2 |
| [Mapstruct](https://github.com/mapstruct/mapstruct/) | Java bean mappings | 1.4.1.Final |
| [Guava](https://github.com/google/guava) | Google core libraries for Java | 30.1.1-jre |
| [Jcasbin](https://github.com/casbin/casbin) | An authorization library | 1.13.2 |
| [Sa-Token](https://sa-token.dev33.cn/) | Sa-Token 是一个轻量级 Java 权限认证框架 | 1.2.6 |
| [Feign](https://github.com/spring-cloud/spring-cloud-openfeign) | Feign makes writing java http clients easier | 2.2.7.RELEASE |
| [Swagger2](https://github.com/SpringForAll/spring-boot-starter-swagger) | Api doc | 1.9.0.RELEASE |
| [Easyexcel](https://www.yuque.com/easyexcel/doc/easyexcel) | 快速、简单避免OOM的java处理Excel工具 | 2.2.10 |


### 接下来计划引入依赖
* [ ] 调度任务控制 power-job
* [ ] 消息队列 Rocket mq
* [ ] 搜索引擎 Elasticsearch
* [ ] 网关 spring cloud gateway2
* [ ] api文档管理 YApi
* [ ] 分布式事务解决 Seata
* [ ] APM SkyWalking
* [ ] 分布式文件存储 MongoDB


## 开发说明
### 项目概述
本脚手架项目是典型的rpc架构，xxx-api模块仅仅是定义了接口，xxx-server才是核心服务的实现。下面主要说明在xxx-server模块中的开发约定，xxx-server的分层设计参考了部分DDD的设计思想，
但是也能允许根据业务实际情况，进行结构调整。

### 项目分层
1）适配层（Adapter Layer）：负责对前端展示（web，wireless，wap）的路由和适配，对于传统B/S系统而言，adapter就相当于MVC中的controller。
这层中不允许存在业务逻辑，只允许存在接口路由和一些请求入参的适配转换（只是必要情况下的数据转换，不做入参校验），直接将application的响应返回抛出。

2）应用层（Application Layer）：服务的主入口，负责获取输入，组装上下文，参数校验，主要业务逻辑实现。实际开发中可能会分为两种情况，当简单业务逻辑时，可以直接采用MVC传统分层思想，将这层当做service层，
写写事务流水代码就好。当业务逻辑复杂时，仔细分析业务逻辑，逻辑拆分，在条件允许情况的情况，强烈建议借鉴DDD的设计思想， 进行领域实体建模。
数据交互逻辑说明，关于DB的数据允许直接调用IService/BaseMapper;除基础DB之外的数据交互，约定采用数据网关层的调用。事务说明，业务逻辑中所有事务的传播最终在这层收敛。

3）领域层（Domain Layer）: 当采用DDD设计思想时，这层才具有价值，否则这层可为空。这层主要是封装了核心业务逻辑，并通过领域服务（Domain Service）和领域对象（Domain Entity）的方法对Application层提供业务实体和业务逻辑计算。

4）基础设施层（Infrastructure Layer）：服务基础依赖，不允许存在和业务逻辑相关的代码，只为业务逻辑服务。比如各种集成框架的config,调用第三方服务的http实现等等，就是要讲和核心业务逻辑无关的基础服务进行
剥离，
4.1) 基础网关层（infrastructure-gateway）: 服务基础网关。目前的架构为了开发效率考虑，其他层允许有限的访问基础层中的稳定依赖，如果判断某项基础依赖很有可能后期发生变化，那么还是应该避免与其发生直接依赖；
可以通过在gateway建立稳定抽象，基础层提供实现的方式来解耦；

### 代码规约
结合 COlA-component ，参考alibaba《java开发手册（嵩山版）》
